import http from '@/lib/http';

export interface IUserInfo {
  login: string;
  name: string;
}

export interface IProjectTeamItem {
  tenantID?: string;
  name?: string;
  tenantName: string;
  tenantId: string;
  llmServiceAppId: Array<string>;
}
// 登录信息
export function getUserInfo(): Promise<IUserInfo> {
  return http({
    url: '/web/v1/sso',
    method: 'get',
  });
}

export interface IModelListResponse {
  data: string; // 实际返回的是包含模型列表的 JSON 字符串
  message: string;
  status: number;
}

// 获取模型列表
export function getModelList(): Promise<IModelListResponse> {
  return http({
    url: '/weiwei/model-list',
    method: 'get',
  });
}

export interface IConversationItem {
  conversationId: string;
  creatorMis: string;
  creatorName: string;
  title: string;
  model: string;
  messageCount: number;
  status: number;
  agentId: string;
  addTime: string;
  updateTime: string;
  appfactoryConversationId: string | null;
  conversationType: string | null;
  isEncryptionMode: boolean;
}

export interface IModelInfo {
  modelName: string;
  modelLabel: string;
  imageUrl?: string;
}

export interface IAgentInfo {
  agentId: string;
  agentName: string;
  agentIconUrl: string;
  systemPrompt: string;
  welcomeMessage: string;
  modelList: IModelInfo[];
  defaultModel: string;
  description: string;
  sort: number;
  safeModeSupported: boolean;
  fileUploadButton: boolean;
  generalAgentTypeEnum: string;
}

// 历史会话列表响应接口（新的后端响应格式）
export interface IConversationListResponse {
  success: boolean;
  user_id: string;
  summary: Record<string, string>; // key是conversation_id，value是对话标题
}

// 获取历史会话记录
export async function getConversationList(userId: string): Promise<IConversationListResponse> {
  return http({
    url: `/humanrelation/conversations?user_id=${userId}`,
    method: 'get',
  });
}

export interface IMessageItem {
  messageId: string;
  conversationId: string;
  role: string;
  parentId: string;
  model: string | null;
  sort: number;
  status: number;
  vote: number;
  pluginCode: string;
  userSelectPluginCode: string;
  addTime: string;
  updateTime: string;
  memoryUpdate: number;
  imMessageId: string | null;
  source: number;
  canvasId: string | null;
  content: string;
  pluginInfoContent: string;
  image: number;
  pluginInfo: IPluginInfo | null;
  userSelectPluginInfo: IPluginInfo[] | null;
  agentInfo: {
    agentId: string;
    agentName: string;
    agentIconUrl: string;
  };
  fileInfo: IFileInfo | null;
  file: IFileInfo | null;
  canvasContent: string | null;
  reasoning_content: string | null;
}

export interface IPluginInfo {
  pluginCode: string;
  pluginName: string;
  pluginIconUrl: string;
  loadingText: string;
  finishedText: string;
  k: number;
  description: string;
  content: string | null;
}

export interface IFileInfo {
  fileId: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  fileType: string;
  uploadTime: string;
}

export interface IConversationDetail {
  conversationId: string;
  creatorMis: string;
  creatorName: string;
  title: string;
  model: string;
  messageCount: number;
  status: number;
  addTime: string;
  updateTime: string;
  messageList: IMessageItem[];
}

// 获取会话详情
export function getConversationDetail(conversationId: string): Promise<IConversationDetail> {
  return http({
    url: `/web/v1/conversation/detail?conversationId=${conversationId}`,
    method: 'get',
  });
}

export interface IEditConversationRequest {
  conversationId: string;
  title: string;
}

export interface IEditConversationResponse {
  status: number;
  message: string;
  data: boolean;
}

// 编辑会话标题
export function editConversationTitle(
  params: IEditConversationRequest,
): Promise<IEditConversationResponse> {
  return http({
    url: '/web/v1/conversation/edit',
    method: 'post',
    data: params,
  });
}
// 历史聊天记录相关接口
export interface IHistoryMessage {
  type: 'human' | 'ai';
  content: string;
  additional_kwargs: Record<string, unknown>;
}

export interface IConversationHistoryResponse {
  conversation_id: string;
  history: IHistoryMessage[];
  status: string;
}

// 获取历史聊天记录
export function getConversationHistory(
  conversationId: string,
  userId: string,
): Promise<IConversationHistoryResponse> {
  return http({
    url: `/humanrelation/history?conversation_id=${conversationId}&user_id=${userId}`,
    method: 'get',
  });
}

// 删除对话请求接口
export interface IDeleteConversationRequest {
  user_id: string;
  conversation_id: string;
}

// 删除对话响应接口
export interface IDeleteConversationResponse {
  success: boolean;
  message: string;
  user_id: string;
  conversation_id: string;
}

// 删除对话
export function deleteConversation(
  params: IDeleteConversationRequest,
): Promise<IDeleteConversationResponse> {
  return http({
    url: '/humanrelation/conversation',
    method: 'delete',
    data: params,
  });
}
