<template>
  <div class="assistant-introduction-container">
    <!-- 助手介绍区域 -->
    <div class="assistant-desc-rotator">
      <div class="desc-content">
        <!-- 左侧活跃助手头像 -->
        <div
          v-if="activeAssistant"
          class="active-assistant-avatar"
          :class="{
            'avatar-entering': isAvatarEntering,
            'avatar-leaving': isAvatarLeaving,
          }"
        >
          <div class="avatar-circle">
            <img :src="activeAssistant.image" :alt="activeAssistant.title" class="avatar-img" />
          </div>
          <div class="assistant-name">{{ activeAssistant.title }}</div>
        </div>

        <!-- 右侧文字介绍 -->
        <div class="desc-text" :class="{ 'with-avatar': activeAssistant }">
          <strong class="desc-badge">各个助手的自我介绍<br /></strong>
          <span class="desc-content-text" :class="{ typing: isTyping }">{{ displayText }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';

// Props
interface IAssistantFeature {
  id: number;
  title: string;
  image: string;
}

interface IProps {
  assistantIntros: string[];
  availableFeatures: IAssistantFeature[];
  futureFeatures: IAssistantFeature[];
  topFeatures?: {
    id: number;
    title: string;
    image: string;
    processedImage?: string;
  }[];
}

const props = defineProps<IProps>();

// 状态管理
const currentIntroIndex = ref(0);
const displayText = ref('');
const activeAssistant = ref<IAssistantFeature | null>(null);
const isAvatarEntering = ref(false);
const isAvatarLeaving = ref(false);
const isTyping = ref(false);
const avatarStartPosition = ref<{ x: number; y: number } | null>(null);

// 计时器
let introTimer: number | null = null;
let typewriterTimer: number | null = null;

// 合并所有助手数据
const allAssistants = computed(() => {
  const tops = props.topFeatures
    ? props.topFeatures.map((t) => ({
        id: t.id,
        title: t.title,
        image: t.image,
      }))
    : [];
  return [...props.availableFeatures, ...props.futureFeatures, ...tops];
});

// 获取当前助手
const getCurrentAssistant = (index: number): IAssistantFeature | null => {
  const assistantMapping = [
    { title: '董亲友', originalTitle: '董亲友' },
    { title: '董问题', originalTitle: '董问题' },
    { title: '董天奇', originalTitle: '董天奇' },
    { title: '董理财', originalTitle: '董理财' },
    { title: '董追剧', originalTitle: '董追剧' },
    { title: '董外卖', originalTitle: '董外卖' },
    { title: '董穿搭', originalTitle: '董穿搭' },
    { title: '董学习', originalTitle: '董学习' },
    { title: '董出行', originalTitle: '董出行' },
    { title: '董健身', originalTitle: '董健身' },
    { title: '董美食', originalTitle: '董美食' },
    { title: '董家务', originalTitle: '董家务' },
  ];

  const mapping = assistantMapping[index];
  if (!mapping) return null;

  // 调试输出
  console.log(
    '🔍 [AssistantIntroduction] 查找助手:',
    mapping,
    'allAssistants:',
    allAssistants.value,
  );

  return allAssistants.value.find((assistant) => assistant.title === mapping.originalTitle) || null;
};

// 隐藏/显示原位置的功能头像
const hideOriginalAvatar = (assistant: IAssistantFeature) => {
  if (!assistant) return;

  // 查找所有功能头像，隐藏匹配的那个
  const featureItems = document.querySelectorAll('.feature-item');
  featureItems.forEach((item) => {
    const img = item.querySelector('.feature-avatar-img') as HTMLImageElement;
    const label = item.querySelector('.feature-label') as HTMLElement;

    if (img && label && label.textContent?.trim() === assistant.title) {
      console.log('🫥 [AssistantIntroduction] 隐藏原位置头像:', assistant.title);

      // 获取原位置坐标 - 简化计算，直接相对于视窗
      const rect = item.getBoundingClientRect();
      const targetRect = document.querySelector('.desc-text')?.getBoundingClientRect();

      if (targetRect) {
        avatarStartPosition.value = {
          // 从原头像中心到目标位置的偏移
          x: rect.left + rect.width / 2 - (targetRect.left - 30), // 目标位置是文字框左侧-30px
          y: rect.top + rect.height / 2 - (targetRect.top + 20), // 目标位置是文字框顶部+20px
        };
        console.log('📍 [AssistantIntroduction] 原位置坐标:', avatarStartPosition.value);
      }

      item.classList.add('avatar-moved');
    }
  });
};

const showOriginalAvatar = (assistant: IAssistantFeature) => {
  if (!assistant) return;

  // 查找所有功能头像，显示匹配的那个
  const featureItems = document.querySelectorAll('.feature-item');
  featureItems.forEach((item) => {
    const img = item.querySelector('.feature-avatar-img') as HTMLImageElement;
    const label = item.querySelector('.feature-label') as HTMLElement;

    if (img && label && label.textContent?.trim() === assistant.title) {
      console.log('👀 [AssistantIntroduction] 显示原位置头像:', assistant.title);
      item.classList.remove('avatar-moved');
    }
  });
};

// 打字机效果
const typeText = (text: string): Promise<void> => {
  return new Promise((resolve) => {
    displayText.value = '';
    isTyping.value = true;
    let charIndex = 0;

    const typeChar = () => {
      if (charIndex < text.length) {
        displayText.value += text[charIndex];
        charIndex++;
        typewriterTimer = window.setTimeout(typeChar, 120); // 再调慢到120ms per character，让头像静止更明显
      } else {
        isTyping.value = false;
        resolve();
      }
    };

    typeChar();
  });
};

// 头像进入动画
const showAssistantAvatar = async (assistant: IAssistantFeature): Promise<void> => {
  console.log('🎬 [AssistantIntroduction] 显示助手头像:', assistant);

  // 隐藏原位置的头像并获取坐标
  hideOriginalAvatar(assistant);

  return new Promise((resolve) => {
    activeAssistant.value = assistant;

    // 等待DOM更新后设置动画起始位置和开始动画
    setTimeout(() => {
      const avatarElement = document.querySelector('.active-assistant-avatar') as HTMLElement;
      if (avatarElement && avatarStartPosition.value) {
        avatarElement.style.setProperty('--start-x', `${avatarStartPosition.value.x}px`);
        avatarElement.style.setProperty('--start-y', `${avatarStartPosition.value.y}px`);
        console.log('🎬 [AssistantIntroduction] 设置动画起始位置:', avatarStartPosition.value);
      }

      isAvatarEntering.value = true;
      isAvatarLeaving.value = false;

      console.log('🎬 [AssistantIntroduction] 头像状态设置完成:', {
        activeAssistant: activeAssistant.value,
        isAvatarEntering: isAvatarEntering.value,
        isAvatarLeaving: isAvatarLeaving.value,
      });
    }, 50); // 等待DOM更新

    // 动画完成后解析Promise，头像保持在位置
    setTimeout(() => {
      isAvatarEntering.value = false;
      console.log('🎬 [AssistantIntroduction] 头像进入动画完成，头像保持在位置');
      resolve();
    }, 850); // 总时间：50ms等待 + 800ms动画
  });
};

// 头像离开动画
const hideAssistantAvatar = async (): Promise<void> => {
  return new Promise((resolve) => {
    if (!activeAssistant.value) {
      resolve();
      return;
    }

    const currentAssistant = activeAssistant.value;
    isAvatarLeaving.value = true;

    setTimeout(() => {
      // 恢复原位置头像的显示
      showOriginalAvatar(currentAssistant);

      activeAssistant.value = null;
      isAvatarEntering.value = false;
      isAvatarLeaving.value = false;
      resolve();
    }, 800); // 与CSS动画时间一致
  });
};

// 播放单个助手介绍
const playAssistantIntro = async (index: number) => {
  const assistant = getCurrentAssistant(index);
  const introText = props.assistantIntros[index];

  console.log(
    '🎭 [AssistantIntroduction] 开始播放助手介绍:',
    index,
    assistant,
    introText ? `${introText.substring(0, 20)}...` : '无介绍文本',
  );

  if (!assistant || !introText) {
    console.warn('⚠️ [AssistantIntroduction] 助手或介绍文本缺失:', {
      assistant,
      hasIntroText: !!introText,
    });
    return;
  }

  // 1. 头像移动到位置并等待动画完成
  console.log('🎭 步骤1: 头像移动到位置');
  await showAssistantAvatar(assistant);

  // 2. 立即开始打字机效果（头像已经在目标位置）
  console.log('🎭 步骤2: 开始打字机效果');
  await typeText(introText);

  // 3. 打字完成，等待2秒让用户阅读
  console.log('🎭 步骤3: 打字完成，等待阅读时间');
  await new Promise((resolve) => {
    setTimeout(() => resolve(undefined), 2000);
  });

  // 4. 头像移回原位
  console.log('🎭 步骤4: 头像移回原位');
  await hideAssistantAvatar();

  // 5. 停顿1秒再开始下一个
  console.log('🎭 步骤5: 停顿后开始下一个');
  await new Promise((resolve) => {
    setTimeout(() => resolve(undefined), 1000);
  });
};

// 开始介绍循环
/* eslint-disable no-await-in-loop */
const startIntroductionCycle = async () => {
  // 首次启动时等待1秒
  await new Promise((resolve) => {
    setTimeout(() => resolve(undefined), 1000);
  });

  for (;;) {
    for (let i = 0; i < props.assistantIntros.length; i++) {
      currentIntroIndex.value = i;
      // eslint-disable-next-line no-await-in-loop
      await playAssistantIntro(i);
    }
    // 完成一轮后暂停3秒再开始下一轮
    // eslint-disable-next-line no-await-in-loop
    await new Promise((resolve) => {
      setTimeout(() => resolve(undefined), 3000);
    });
  }
};
/* eslint-enable no-await-in-loop */

// 清理定时器
const clearTimers = () => {
  if (introTimer) {
    window.clearTimeout(introTimer);
    introTimer = null;
  }
  if (typewriterTimer) {
    window.clearTimeout(typewriterTimer);
    typewriterTimer = null;
  }
};

onMounted(() => {
  console.log('🚀 [AssistantIntroduction] 组件挂载，props:', props);
  // 延迟3秒开始介绍循环，确保页面完全加载
  setTimeout(() => {
    console.log('🚀 [AssistantIntroduction] 开始介绍循环');
    void startIntroductionCycle();
  }, 3000);
});

onUnmounted(() => {
  clearTimers();
});
</script>

<style lang="scss" scoped>
.assistant-introduction-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.assistant-desc-rotator {
  position: absolute;
  top: calc(100% - 72px);
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: none;
  display: flex;
  justify-content: center;
  z-index: 11;
  flex-direction: column;
  align-items: center;
  perspective: 1000px;
}

.desc-content {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
}

.active-assistant-avatar {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  left: -30px; // 向右移动，更接近文字框
  top: 20px; // 向下移动一些
  transform: translateY(0); // 移除垂直居中
  opacity: 0;
  transition: none;
  z-index: 1000; // 大幅提高z-index，确保能飞过所有其他头像
  pointer-events: none; // 避免干扰其他元素

  &.avatar-entering {
    opacity: 1;
    animation: avatarSlideIn 0.8s ease-out forwards;
  }

  &.avatar-leaving {
    animation: avatarSlideOut 0.8s ease-in forwards;
  }

  // 头像移动完成后的静止状态 - 在目标位置
  &:not(.avatar-entering):not(.avatar-leaving) {
    opacity: 1;
    transform: translate(-20px, 0px);
    animation: none !important;
    transition: none;
  }

  .avatar-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.8); // 稍微减薄边框
    box-shadow: none; // 完全移除光晕和阴影
    margin-bottom: 8px;

    .avatar-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .assistant-name {
    font-size: 18px;
    font-weight: 700;
    color: var(--page-text-primary);
    // text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    white-space: nowrap;
  }
}

.desc-text {
  display: inline-block;
  font-size: clamp(20px, 2.8vw, 30px);
  font-weight: 700;
  color: var(--page-text-primary);
  // text-shadow: 0 1px 2px rgba(0, 0, 0, 0.25);
  line-height: 1.6;
  padding: 10px 14px;
  border-radius: 12px;
  /* 半透明磨砂：改为淡黄色系 */
  background: linear-gradient(
    180deg,
    rgba(255, 245, 157, 0.16),
    /* 浅黄 */ rgba(255, 245, 157, 0.1)
  );
  border: 1px solid rgba(255, 213, 79, 0.35);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.22),
    inset 0 0 24px rgba(255, 213, 79, 0.12);
  backdrop-filter: blur(10px) saturate(120%);
  width: 60vw;
  max-width: 60vw;
  min-width: 50vw;
  white-space: normal;
  word-break: break-word;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  line-clamp: 5;
  position: relative;
  transition: all 0.3s ease;
  text-align: left; // 整个文字框内容居左对齐

  &.with-avatar {
    margin-left: 50px; // 向右移动50px，避免与头像重合
  }

  &::before {
    content: '';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 12px solid rgba(255, 213, 79, 0.25);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.35));
  }

  .desc-badge {
    margin-right: 8px;
    color: var(--page-text-primary);
    font-weight: 800;
    display: block; // 让标题独占一行
    text-align: center; // 标题居中展示
    margin-bottom: 8px; // 标题与内容间增加间距
  }

  .desc-content-text {
    min-height: 1.6em; // 确保文字区域有最小高度
    text-align: left; // 内容文字居左对齐
    display: block; // 确保文字块级显示
  }
}

// 头像动画 - 简单的从原位置到目标位置
@keyframes avatarSlideIn {
  0% {
    opacity: 1;
    transform: translate(var(--start-x, 0px), var(--start-y, 0px));
  }

  100% {
    opacity: 1;
    transform: translate(-20px, 0px);
  }
}

@keyframes avatarSlideOut {
  0% {
    opacity: 1;
    transform: translate(-20px, 0px);
  }

  100% {
    opacity: 1;
    transform: translate(var(--start-x, 0px), var(--start-y, 0px));
  }
}

// 打字机光标效果 - 只在打字时显示
.desc-content-text.typing::after {
  content: '|';
  animation: blink 1s infinite;
  color: currentColor;
  font-weight: 800;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }

  51%,
  100% {
    opacity: 0;
  }
}

// 隐藏被移动的原位置头像的样式 - 直接消失
:global(.feature-item.avatar-moved) {
  opacity: 0; // 完全隐藏原位置头像
  visibility: hidden; // 确保完全不可见
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease; // 快速消失
  pointer-events: none; // 禁用交互
}

// 确保原位置头像恢复时保持原有的光晕效果
:global(.feature-item:not(.avatar-moved) .avatar-circle) {
  // 保持原有的光晕和阴影，不受移动头像样式影响
  box-shadow: inherit; // 继承原有的box-shadow
}

// 确保原位置头像在移动过程中光晕留在原处（通过伪元素实现）
:global(.feature-item.avatar-moved::after) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  box-shadow:
    0 8px 18px rgba(0, 0, 0, 0.35),
    0 0 16px rgba(0, 255, 255, 0.25); // 保持光晕在原位置
  pointer-events: none;
  z-index: -1; // 在头像下方
}
</style>
